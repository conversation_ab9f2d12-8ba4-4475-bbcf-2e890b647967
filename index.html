<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AL-SALAMAT - شركة زجاج السيارات</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="realtime-styles.css">

    <!-- Firebase CDN for contact form -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <!-- Homepage Authentication Manager -->
    <script src="homepage-auth.js"></script>

    <!-- Dynamic Content Manager -->
    <script src="dynamic-content.js"></script>

    <!-- Enhanced Real-time Updates -->
    <script src="enhanced-realtime-updates.js"></script>

    <!-- Connection Manager -->
    <script src="connection-manager.js"></script>

    <!-- Firebase Optimization -->
    <script src="firebase-optimization.js"></script>

    <!-- Branches Display Fix - Backup Solution -->
    <script src="branches-display-fix.js"></script>

    <!-- Gallery Manager -->
    <script src="gallery-manager.js"></script>

    <!-- Image Modal Manager -->
    <script src="image-modal.js"></script>
</head>
<body>
    <header>
        <nav class="container">
            <div class="logo">AL-SALAMAT</div>
            <button class="mobile-menu-btn" onclick="toggleSidebar()">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <ul class="nav-links desktop-nav">
                <li id="login-menu-item">
                    <a href="login.html" class="login-link">التسجيل</a>
                    <div class="user-menu" id="user-menu" style="display: none;">
                        <button class="user-menu-btn" onclick="toggleUserDropdown()">تم التسجيل ▼</button>
                        <div class="user-dropdown" id="user-dropdown">
                            <div class="user-dropdown-item status">تم التسجيل</div>
                            <div class="user-dropdown-item admin-link" id="admin-link" style="display: none;" onclick="window.location.href='admin.html'">لوحة الإدارة</div>
                            <div class="user-dropdown-item logout" onclick="logout()">تسجيل الخروج</div>
                        </div>
                    </div>
                </li>
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#about">من نحن</a></li>
                <li><a href="#gallery">معرض الصور</a></li>
                <li><a href="#branches">الفروع</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
        </nav>
    </header>

    <!-- Mobile Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-logo">AL-SALAMAT</div>
            <button class="close-btn" onclick="toggleSidebar()">&times;</button>
        </div>
        <div id="sidebar-login-section">
            <a href="login.html" class="sidebar-login-link">التسجيل</a>
        </div>
        <div class="sidebar-user-menu" id="sidebar-user-menu" style="display: none;">
            <div class="sidebar-user-status">تم التسجيل</div>
            <div class="sidebar-logout-btn" onclick="logout()">تسجيل الخروج</div>
        </div>
        <ul class="sidebar-nav">
            <li><a href="#home" onclick="toggleSidebar()">الرئيسية</a></li>
            <li><a href="#about" onclick="toggleSidebar()">من نحن</a></li>
            <li><a href="#gallery" onclick="toggleSidebar()">معرض الصور</a></li>
            <li><a href="#branches" onclick="toggleSidebar()">الفروع</a></li>
            <li><a href="#contact" onclick="toggleSidebar()">اتصل بنا</a></li>
        </ul>
    </div>

    <!-- Overlay -->
    <div class="overlay" id="overlay" onclick="toggleSidebar()"></div>

    <!-- Image Modal -->
    <div class="image-modal" id="imageModal" onclick="closeImageModal()">
        <div class="image-modal-content" onclick="event.stopPropagation()">
            <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
            <img class="image-modal-img" id="modalImage" src="" alt="">
            <div class="image-modal-info">
                <h3 id="modalTitle"></h3>
                <p id="modalDescription"></p>
            </div>
        </div>
    </div>



    <main class="container">
        <section id="home" class="hero-section">
            <h1 class="hero-title" id="company-title">AL-SALAMAT</h1>
            <h2 class="hero-subtitle" id="company-subtitle">رائدة في زجاج السيارات</h2>
            <img src="img/main1.jpg" alt="زجاج السيارات" class="company-image">

            <!-- Gallery Images -->
            <div class="gallery-section">
                <div class="gallery-grid" id="dynamic-gallery">
                    <!-- سيتم تحميل الصور من Firebase -->
                    <div class="gallery-item">
                        <img src="img/car2.png" alt="خدمات زجاج السيارات" class="gallery-image">
                    </div>
                    <div class="gallery-item">
                        <img src="img/car4.png" alt="تركيب زجاج السيارات" class="gallery-image">
                    </div>
                    <div class="gallery-item">
                        <img src="img/car6.png" alt="إصلاح زجاج السيارات" class="gallery-image">
                    </div>

                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="about-section">
            <h2 class="about-title" id="about-title">من نحن</h2>
            <div class="about-content">
                <div class="about-loading" id="about-loading">
                    <div class="loading-spinner"></div>
                    <p>جاري تحميل المحتوى...</p>
                </div>
                <p class="about-description hidden" id="about-description"></p>
            </div>
        </section>

        <!-- Gallery Section -->
        <section id="gallery" class="gallery-main-section">
            <h2 class="gallery-main-title">معرض الصور</h2>
            <div class="gallery-container">
                <!-- Gallery Sidebar -->
                <div class="gallery-sidebar">
                    <h3 class="gallery-sidebar-title">فئات الصور</h3>
                    <ul class="gallery-categories">
                        <li class="gallery-category active" data-category="all">
                            <span class="category-icon">🖼️</span>
                            <span class="category-name">جميع الصور</span>
                        </li>
                        <li class="gallery-category" data-category="services">
                            <span class="category-icon">🔧</span>
                            <span class="category-name">خدماتنا</span>
                        </li>
                        <li class="gallery-category" data-category="installation">
                            <span class="category-icon">🚗</span>
                            <span class="category-name">التركيب</span>
                        </li>
                        <li class="gallery-category" data-category="repair">
                            <span class="category-icon">🛠️</span>
                            <span class="category-name">الإصلاح</span>
                        </li>
                        <li class="gallery-category" data-category="products">
                            <span class="category-icon">💎</span>
                            <span class="category-name">منتجاتنا</span>
                        </li>
                    </ul>

                    <!-- Gallery Stats -->
                    <div class="gallery-stats">
                        <div class="stat-item">
                            <span class="stat-number" id="total-images">0</span>
                            <span class="stat-label">إجمالي الصور</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number" id="selected-category-count">0</span>
                            <span class="stat-label">الفئة المحددة</span>
                        </div>
                    </div>
                </div>

                <!-- Gallery Content -->
                <div class="gallery-content">
                    <div class="gallery-header">
                        <h3 class="current-category-title">جميع الصور</h3>
                        <div class="gallery-controls">
                            <button class="gallery-control-btn" id="grid-view-btn" title="عرض شبكي">
                                <span>⊞</span>
                            </button>
                            <button class="gallery-control-btn" id="list-view-btn" title="عرض قائمة">
                                <span>☰</span>
                            </button>
                        </div>
                    </div>

                    <div class="gallery-main-grid" id="main-gallery-grid">
                        <!-- سيتم تحميل الصور من Firebase -->
                        <div class="gallery-loading" id="gallery-loading">
                            <div class="loading-spinner"></div>
                            <p>جاري تحميل الصور...</p>
                        </div>

                        <!-- Default Images -->
                        <div class="gallery-main-item" data-category="services" onclick="openImageModal('img/car2.png', 'خدمات زجاج السيارات', 'خدمات متميزة لزجاج السيارات')">
                            <img src="img/car2.png" alt="خدمات زجاج السيارات" class="gallery-main-image">
                            <div class="gallery-item-overlay">
                                <h4>خدمات زجاج السيارات</h4>
                                <p>خدمات متميزة لزجاج السيارات</p>
                                <div class="gallery-item-actions">
                                    <span class="view-icon">👁️</span>
                                    <span class="view-text">عرض</span>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-main-item" data-category="installation" onclick="openImageModal('img/car4.png', 'تركيب زجاج السيارات', 'تركيب احترافي بأعلى جودة')">
                            <img src="img/car4.png" alt="تركيب زجاج السيارات" class="gallery-main-image">
                            <div class="gallery-item-overlay">
                                <h4>تركيب زجاج السيارات</h4>
                                <p>تركيب احترافي بأعلى جودة</p>
                                <div class="gallery-item-actions">
                                    <span class="view-icon">👁️</span>
                                    <span class="view-text">عرض</span>
                                </div>
                            </div>
                        </div>

                        <div class="gallery-main-item" data-category="repair" onclick="openImageModal('img/car6.png', 'إصلاح زجاج السيارات', 'إصلاح سريع وموثوق')">
                            <img src="img/car6.png" alt="إصلاح زجاج السيارات" class="gallery-main-image">
                            <div class="gallery-item-overlay">
                                <h4>إصلاح زجاج السيارات</h4>
                                <p>إصلاح سريع وموثوق</p>
                                <div class="gallery-item-actions">
                                    <span class="view-icon">👁️</span>
                                    <span class="view-text">عرض</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="branches" class="branches-section">
            <h2 class="branches-title">فروعنا</h2>
            <div class="branches-grid" id="dynamic-branches">
                <!-- سيتم تحميل الفروع من Firebase -->
                <div class="no-data-message" id="no-branches-message">
                    <p>لا توجد فروع حالياً</p>
                </div>
            </div>
        </section>

        <!-- Separator -->
        <div class="section-separator"></div>

        <!-- Contact Section -->
        <section id="contact" class="contact-section">
            <h2 class="contact-title" id="contact-title">اتصل بنا</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3 id="contact-info-title">معلومات التواصل</h3>
                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div class="contact-details">
                            <strong>الهاتف:</strong>
                            <span id="contact-phone-display">0100000000</span>
                            <a href="tel:0100000000" class="contact-action-btn">اتصل الآن</a>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <strong>البريد الإلكتروني:</strong>
                            <span id="contact-email-display"><EMAIL></span>
                            <a href="mailto:<EMAIL>" class="contact-action-btn">إرسال بريد</a>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <strong>العنوان:</strong>
                            <span id="contact-address-display">المملكة العربية السعودية</span>
                        </div>
                    </div>
                    <div class="contact-item">
                        <div class="contact-icon">🕒</div>
                        <div class="contact-details">
                            <strong>ساعات العمل:</strong>
                            <span id="contact-hours-display">السبت - الخميس: 8:00 ص - 10:00 م</span>
                        </div>
                    </div>
                </div>

                <div class="contact-form-container">
                    <h3>أرسل لنا رسالة</h3>
                    <form id="contact-form" class="contact-form">
                        <div class="form-group">
                            <label for="contact-name">الاسم</label>
                            <input type="text" id="contact-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-email">البريد الإلكتروني</label>
                            <input type="email" id="contact-email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-phone">رقم الهاتف</label>
                            <input type="tel" id="contact-phone" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label for="contact-message">الرسالة</label>
                            <textarea id="contact-message" name="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="contact-submit-btn">إرسال الرسالة</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 شركة AL-SALAMAT لزجاج السيارات. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        console.log('Firebase initialized successfully');

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            sidebar.classList.toggle('active');
            overlay.classList.toggle('active');
        }

        // Close sidebar when clicking on a link
        document.querySelectorAll('.sidebar-nav a').forEach(link => {
            link.addEventListener('click', () => {
                toggleSidebar();
            });
        });

        // Toggle user dropdown menu
        function toggleUserDropdown() {
            const dropdown = document.getElementById('user-dropdown');
            dropdown.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('user-menu');
            const dropdown = document.getElementById('user-dropdown');

            if (userMenu && !userMenu.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Check if user is logged in and update login button
        function checkLoginStatus() {
            const user = localStorage.getItem('user');

            if (user) {
                const userData = JSON.parse(user);
                const userName = userData.displayName || 'المستخدم';

                // Show user menu in desktop nav
                document.querySelector('.login-link').style.display = 'none';
                document.getElementById('user-menu').style.display = 'inline-block';

                // Show user menu in sidebar
                document.getElementById('sidebar-login-section').style.display = 'none';
                document.getElementById('sidebar-user-menu').style.display = 'block';

                // Check if user is admin and show admin link
                checkAdminStatus(userData);
            } else {
                // Show login links
                document.querySelector('.login-link').style.display = 'inline-block';
                document.getElementById('user-menu').style.display = 'none';

                // Show login link in sidebar
                document.getElementById('sidebar-login-section').style.display = 'block';
                document.getElementById('sidebar-user-menu').style.display = 'none';
            }
        }

        // Check if user has admin privileges
        async function checkAdminStatus(userData) {
            try {
                // Admin emails with full access
                const adminEmails = ['<EMAIL>']; // Main admin email

                if (adminEmails.includes(userData.email) || userData.displayName === 'admin') {
                    document.getElementById('admin-link').style.display = 'block';
                }
            } catch (error) {
                console.error('Error checking admin status:', error);
            }
        }

        // Logout function
        function logout() {
            if (confirm('هل تريد تسجيل الخروج؟')) {
                localStorage.removeItem('user');

                // Hide user menus and show login links
                document.querySelector('.login-link').style.display = 'inline-block';
                document.getElementById('user-menu').style.display = 'none';

                // Hide sidebar user menu and show login link
                document.getElementById('sidebar-login-section').style.display = 'block';
                document.getElementById('sidebar-user-menu').style.display = 'none';

                // Close dropdown if open
                document.getElementById('user-dropdown').classList.remove('show');

                alert('تم تسجيل الخروج بنجاح');
            }
        }

        // Handle contact form submission
        document.getElementById('contact-form').addEventListener('submit', async function(event) {
            event.preventDefault();

            const name = document.getElementById('contact-name').value;
            const email = document.getElementById('contact-email').value;
            const phone = document.getElementById('contact-phone').value;
            const message = document.getElementById('contact-message').value;

            const submitBtn = document.querySelector('.contact-submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'جاري الإرسال...';
            submitBtn.disabled = true;

            try {
                // Save message to Firebase (if Firebase is available)
                if (typeof firebase !== 'undefined') {
                    const database = firebase.database();
                    await database.ref('contactForms').push({
                        name: name,
                        email: email,
                        phone: phone,
                        message: message,
                        submittedAt: new Date().toISOString(),
                        status: 'new',
                        userAgent: navigator.userAgent
                    });
                }

                alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
                document.getElementById('contact-form').reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            } catch (error) {
                console.error('Error sending message:', error);
                alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
                document.getElementById('contact-form').reset();
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });

        // All content updates are now handled by dynamic-content.js

        // Gallery Management Functions
        function initializeGallery() {
            const categories = document.querySelectorAll('.gallery-category');
            const galleryItems = document.querySelectorAll('.gallery-main-item');
            const currentCategoryTitle = document.querySelector('.current-category-title');
            const totalImagesCount = document.getElementById('total-images');
            const selectedCategoryCount = document.getElementById('selected-category-count');
            const gridViewBtn = document.getElementById('grid-view-btn');
            const listViewBtn = document.getElementById('list-view-btn');
            const galleryGrid = document.getElementById('main-gallery-grid');

            // Update counts
            function updateCounts() {
                const allItems = document.querySelectorAll('.gallery-main-item');
                const total = allItems.length;
                totalImagesCount.textContent = total;

                const activeCategory = document.querySelector('.gallery-category.active');
                const categoryName = activeCategory ? activeCategory.dataset.category : 'all';

                if (categoryName === 'all') {
                    selectedCategoryCount.textContent = total;
                } else {
                    const categoryItems = document.querySelectorAll(`.gallery-main-item[data-category="${categoryName}"]`);
                    selectedCategoryCount.textContent = categoryItems.length;
                }
            }

            // Filter gallery items
            function filterGallery(category) {
                const allItems = document.querySelectorAll('.gallery-main-item');
                allItems.forEach(item => {
                    if (category === 'all' || item.dataset.category === category) {
                        item.style.display = 'block';
                        item.style.animation = 'fadeInUp 0.5s ease-out';
                    } else {
                        item.style.display = 'none';
                    }
                });
                updateCounts();
            }

            // Category click handlers
            categories.forEach(category => {
                category.addEventListener('click', function() {
                    // Remove active class from all categories
                    categories.forEach(cat => cat.classList.remove('active'));

                    // Add active class to clicked category
                    this.classList.add('active');

                    // Update title
                    const categoryName = this.querySelector('.category-name').textContent;
                    currentCategoryTitle.textContent = categoryName;

                    // Filter gallery
                    filterGallery(this.dataset.category);
                });
            });

            // View toggle handlers
            gridViewBtn.addEventListener('click', function() {
                galleryGrid.classList.remove('list-view');
                gridViewBtn.classList.add('active');
                listViewBtn.classList.remove('active');
            });

            listViewBtn.addEventListener('click', function() {
                galleryGrid.classList.add('list-view');
                listViewBtn.classList.add('active');
                gridViewBtn.classList.remove('active');
            });

            // Initialize counts
            updateCounts();

            // Hide loading
            const galleryLoading = document.getElementById('gallery-loading');
            if (galleryLoading) {
                galleryLoading.style.display = 'none';
            }
        }

        // Image Modal Functions
        function openImageModal(imageSrc, title, description) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');
            const modalDescription = document.getElementById('modalDescription');

            modalImage.src = imageSrc;
            modalImage.alt = title;
            modalTitle.textContent = title;
            modalDescription.textContent = description;

            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';

            // Add animation
            setTimeout(() => {
                modal.classList.add('active');
            }, 10);
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('active');

            setTimeout(() => {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }, 300);
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeImageModal();
            }
        });

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            initializeGallery();
            console.log('📄 Page loaded, Dynamic Content Manager will handle content updates');

            // Force check for branches after a delay to ensure everything is loaded
            setTimeout(() => {
                console.log('🔍 Checking if branches are loaded...');
                const branchesGrid = document.getElementById('dynamic-branches');
                const branchCards = branchesGrid ? branchesGrid.querySelectorAll('.branch-card') : [];
                console.log(`📊 Found ${branchCards.length} branch cards in DOM`);

                if (branchCards.length === 0 && window.dynamicContentManager) {
                    console.log('🔄 No branches found, forcing reload...');
                    window.dynamicContentManager.loadBranches();
                }
            }, 3000);
        });
    </script>
</body>
</html>
